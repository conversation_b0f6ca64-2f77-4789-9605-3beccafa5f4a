import React, { useState } from 'react'

function SignUpForm() {

        const[data,setData]=useState({
            userName : '',
            Password : '',
            ConfirmPassword : '',
            email :''
        })

        const{userName,Password,ConfirmPassword,email}=data

        const ChangeHandler=(e)=>{
            setData({...data,[e.target.name]:e.target.value})
        }

        const handleSubmit=(e)=>{
            console.log(data);
            e.preventDefault()
            
        }
  return (
    <form onSubmit={handleSubmit}>
    <div>
    <label>userName :</label>
    <input type='text' name='userName' value={userName} onChange={ChangeHandler}/><br/>
    <label>Password :</label>
    <input type='password' name='Password' value={Password} onChange={ChangeHandler}/><br/>
    <label>ConfirmPassword :</label>
    <input type='password' name='ConfirmPassword' value={ConfirmPassword} onChange={ChangeHandler}/><br/>
    {
      ConfirmPassword && Password!=ConfirmPassword?<p style={{'color':'red'}}>Password didnot match</p>:null
    }
    <label>Email :</label>
    <input type='email'  name= 'email' value={email} onChange={ChangeHandler}/><br></br>
    </div>
    <input type='submit' value='submit'/>
    </form>
  )
}

export default SignUpForm