import React, { useEffect, useRef } from 'react'

function Ref1() {
const data = useRef({
    userName : null,
    Email:null
});

const handleSubmit=(e)=>{

    e.preventDefault();
    console.log(data.current.userName.value)
    console.log(data.current.Email.value)
    //data.current.userName='Helo'
}

// useEffect(()=>{
//     data.current.userName.focus()
// })
  return (

    <div>
        <form onSubmit={handleSubmit}>
            <input ref={(r)=>(data.current.userName=r)} type='text' placeholder='Enter UserName'/>
            <input ref={(r)=>(data.current.Email=r)} type='email' placeholder='Enter Email'/>
            <input type='submit' value='submit'/>
        </form>
        
    </div>
  )
}

export default Ref1