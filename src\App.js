import logo from './logo.svg';
import './App.css';
import Hello from './Hello';
import Message from './Message';
import FunMessage from './FunMessage';
import Form from './Form';
import SignUpForm from './SignUpForm';
import Effect from './Effect';
import SampleAxios from './SampleAxios';
import Ref1 from './Ref1';
import { createContext, useState } from 'react';
import ComponentA from './ComponentA';
import ComponentB from './ComponentB';
import ExpensiveCalculation from './Memo';

export const store = createContext()
function App() {
  const[data,setData]=useState(0);
  return (
    
    <div className="App">
        <store.Provider value={[data,setData]}>
          <center>
          <ComponentA/>
          <ComponentB/>
          <button onClick={()=>setData(data+1)}>Click</button>
          </center>
        </store.Provider>

    <ExpensiveCalculation/>








      {/* <Hello/>
      <Message/> */}
      {/* <FunMessage/> */}
      {/* <Form/> */}
      {/* <SignUpForm/> */}
      {/* <Effect/> */}
      {/* <SampleAxios/> */}
      {/* <Ref1/> */}
    </div>
  );
}

export default App;
