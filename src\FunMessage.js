import React, { useState } from 'react'

function FunMessage() {

        const[message,setMessage]=useState({
            message:'Hello'
        });
        const[count,setCount]=useState(0);
  return (
    <div>
        <button onClick={()=>setCount(count+1)}>Count {count}</button>
        <button onClick={()=>setMessage({message:'how r u'})}>Click</button>
        <p>{message.message}</p>
    </div>
  )
}

export default FunMessage