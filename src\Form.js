import React, { useState } from 'react'

function Form() {

    const[userName,setUserName]= useState('')
    const[comments,setComments]=useState('')
    const[courses,setCourses]=useState('')

    const changeNameHandler=(e)=>{
        setUserName(e.target.value)
    }
    const changeCommentHandler=(e)=>{
        setComments(e.target.value)
    }
    const changeCoursesHandler=(e)=>{
        setCourses(e.target.value)
    }
    
    const handleSubmit=(e)=>{
        alert(`${userName} ${comments} ${courses}`)
        e.preventDefault();
    }
  return (
    <form onSubmit={handleSubmit}>
    <div>
        <label>userName :</label>
        <input type='text' value={userName} onChange={changeNameHandler}/>
        <label>Comments :</label>
        <textarea value={comments} onChange={changeCommentHandler}/>
        <select value={courses} onChange={changeCoursesHandler}>
            <option value=''>select</option>
            <option value='react'>React</option>
            <option value='java'>Java</option>
            <option value='Html'>HTMl</option>
        </select>

    </div>
    <input type='submit' value='Submit' />
    </form>
  )
}

export default Form