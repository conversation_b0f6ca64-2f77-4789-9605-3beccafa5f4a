import React, { Component } from 'react'

export default class Message extends Component {

    constructor(){
        super();
        this.state={
            message:"welcome",
            count:0
        }
    }

    change(){
        this.setState({
            message:"Thanks for coming",
            count:this.state.count+1
        })
    }
  render() {
    return (
        
      <div>
        <h1>{this.state.message}</h1>
        <button onClick={()=>this.change()}>Click {this.state.count}</button>
      </div>
    )
  }
}
