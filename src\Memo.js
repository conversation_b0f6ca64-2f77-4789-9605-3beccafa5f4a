import React, { useMemo, useState } from 'react'

const ExpensiveCalculation= ()=>{
    const[count,setCount]=useState(0)
    const[otherState,setOtherState]=useState(0)

    const computeFactorial=(n)=>{
        console.log("computing Facctorial...")
        return n<=1 ? 1 : n* computeFactorial(n-1);
    }
    //Memoizing the factorial
    const Factorial = useMemo(()=>computeFactorial(count),[count])

    return(
        <div>
        <p>Factorial of {count} is : {Factorial}</p>
        <button onClick={()=>setCount(count + 1)}>Increment </button>
        <button onClick={()=>setCount(count - 1)}>Decrement </button>
        <button onClick={()=>setOtherState(otherState+1)}>changeOtherState</button>
        </div>
    )
}
export default ExpensiveCalculation